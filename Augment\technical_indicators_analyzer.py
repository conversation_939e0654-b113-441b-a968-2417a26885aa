"""
Technical Indicators Analyzer using pandas-ta

This comprehensive system analyzes technical indicators for different scenarios:
1. Signals generated by smart_vectorized_backtester copy.py
2. Specific candle times chosen by user
3. Specific time periods
4. Complete market hours

Features:
- Multiple implementation methods (Standard, DataFrame Extension, Study)
- Historical data analysis (2 minutes before signals for trend analysis)
- CLI interface for flexible usage
- Support for all pandas-ta indicators
- Optimized for speed and accuracy
"""

import sys
import os
import logging
import pandas as pd
import pandas_ta as ta
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Union
import json
import argparse
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import backtester for signal generation
try:
    # Try importing with space in filename
    import importlib.util
    spec = importlib.util.spec_from_file_location("smart_vectorized_backtester_copy",
                                                 os.path.join(current_dir, "smart_vectorized_backtester copy.py"))
    smart_vectorized_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(smart_vectorized_module)
    SmartVectorizedBacktester = smart_vectorized_module.SmartVectorizedBacktester
except Exception as e:
    print(f"⚠️ Could not import SmartVectorizedBacktester: {str(e)}")
    SmartVectorizedBacktester = None

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TechnicalIndicatorsAnalyzer:
    """
    Comprehensive Technical Indicators Analysis System

    This system implements all pandas-ta capabilities with multiple methods:
    1. Direct Function Calls (Standard Method)
    2. DataFrame Extension Methods
    3. Strategy-based Methods
    4. Category-based Methods
    5. Custom Strategy Methods
    """

    def __init__(self):
        """Initialize the analyzer"""
        self.supported_methods = {
            'direct_call': 'Direct function calls (ta.indicator_name)',
            'extension': 'DataFrame extension methods (df.ta.indicator_name)',
            'extension_kind': 'DataFrame extension with kind (df.ta(kind="indicator"))',
            'strategy_all': 'All indicators using AllStrategy',
            'strategy_all_automated': 'All indicators automated with cores=6 and timed=True (280+ indicators)',
            'strategy_common': 'Common indicators using CommonStrategy',
            'strategy_category': 'Category-based strategies (candles, momentum, etc.)',
            'custom_strategy': 'Custom strategy with selected indicators'
        }

        # Initialize pandas-ta
        self.df_empty = pd.DataFrame()

        # Get all available indicators and categories
        self.all_indicators = self._get_all_indicators()
        self.categories = self._get_categories()

        # Define category-specific indicators for manual implementation
        self.category_indicators = self._define_category_indicators()

        logger.info(f"✅ Technical Indicators Analyzer initialized")
        logger.info(f"📊 Available indicators: {len(self.all_indicators)}")
        logger.info(f"📂 Categories: {', '.join(self.categories)}")
        logger.info(f"🔧 Supported methods: {len(self.supported_methods)}")
    
    def _get_all_indicators(self) -> List[str]:
        """Get list of all available indicators"""
        try:
            indicators_list = self.df_empty.ta.indicators(as_list=True)
            return indicators_list
        except Exception as e:
            logger.error(f"❌ Error getting indicators list: {str(e)}")
            return []
    
    def _get_categories(self) -> List[str]:
        """Get list of all indicator categories"""
        try:
            # Try different methods to get categories
            if hasattr(self.df_empty.ta, 'categories'):
                categories = self.df_empty.ta.categories
                if callable(categories):
                    return categories()
                else:
                    return categories if isinstance(categories, list) else []
            else:
                # Fallback to predefined categories
                return ['candles', 'cycles', 'momentum', 'overlap', 'performance',
                       'statistics', 'trend', 'volatility', 'volume']
        except Exception as e:
            logger.error(f"❌ Error getting categories: {str(e)}")
            return ['candles', 'cycles', 'momentum', 'overlap', 'performance',
                   'statistics', 'trend', 'volatility', 'volume']

    def _define_category_indicators(self) -> Dict[str, List[Dict]]:
        """Define indicators for each category based on documentation"""
        return {
            'overlap': [
                # Moving Averages and Overlap Studies - Based on documentation
                {'name': 'sma', 'params': {'length': 10}},
                {'name': 'sma', 'params': {'length': 20}},
                {'name': 'sma', 'params': {'length': 50}},
                {'name': 'sma', 'params': {'length': 200}},
                {'name': 'ema', 'params': {'length': 10}},
                {'name': 'ema', 'params': {'length': 20}},
                {'name': 'ema', 'params': {'length': 50}},
                {'name': 'wma', 'params': {'length': 10}},
                {'name': 'hma', 'params': {'length': 10}},
                {'name': 'vwma', 'params': {'length': 10}},
                {'name': 'alma', 'params': {'length': 9, 'sigma': 6.0, 'distribution_offset': 0.85}},
                {'name': 'dema', 'params': {'length': 10}},
                {'name': 'tema', 'params': {'length': 10}},
                {'name': 'trima', 'params': {'length': 10}},
                {'name': 'kama', 'params': {'length': 10, 'fast': 2, 'slow': 30}},
                {'name': 'fwma', 'params': {'length': 10}},
                {'name': 'jma', 'params': {'length': 7, 'phase': 0}},
                {'name': 'linreg', 'params': {'length': 14}},
                {'name': 'midpoint', 'params': {'length': 14}},
                {'name': 'midprice', 'params': {'length': 14}},
                {'name': 'pwma', 'params': {'length': 10}},
                {'name': 'rma', 'params': {'length': 10}},
                {'name': 'sinwma', 'params': {'length': 14}},
                {'name': 'ssf', 'params': {'length': 10, 'poles': 2}},
                {'name': 'swma', 'params': {'length': 5}},
                {'name': 't3', 'params': {'length': 10, 'a': 0.7}},
                {'name': 'vidya', 'params': {'length': 14}},
                {'name': 'zlma', 'params': {'length': 10}},
            ],
            'momentum': [
                # Momentum Indicators - Based on documentation
                {'name': 'rsi', 'params': {'length': 14}},
                {'name': 'rsi', 'params': {'length': 21}},
                {'name': 'macd', 'params': {'fast': 12, 'slow': 26, 'signal': 9}},
                {'name': 'stoch', 'params': {'k': 14, 'd': 3, 'smooth_k': 3}},
                {'name': 'stochrsi', 'params': {'length': 14, 'rsi_length': 14, 'k': 3, 'd': 3}},
                {'name': 'cci', 'params': {'length': 14, 'c': 0.015}},
                {'name': 'willr', 'params': {'length': 14}},
                {'name': 'roc', 'params': {'length': 10}},
                {'name': 'mom', 'params': {'length': 10}},
                {'name': 'ao', 'params': {'fast': 5, 'slow': 34}},
                {'name': 'ppo', 'params': {'fast': 12, 'slow': 26, 'signal': 9}},
                {'name': 'apo', 'params': {'fast': 12, 'slow': 26}},
                {'name': 'bop', 'params': {}},
                {'name': 'cfo', 'params': {'length': 14}},
                {'name': 'cg', 'params': {'length': 10}},
                {'name': 'cmo', 'params': {'length': 14, 'scalar': 100}},
                {'name': 'coppock', 'params': {'length': 11, 'fast': 14, 'slow': 10}},
                {'name': 'cti', 'params': {'length': 12}},
                {'name': 'er', 'params': {'length': 10}},
                {'name': 'eri', 'params': {'length': 13}},
                {'name': 'fisher', 'params': {'length': 9, 'signal': 1}},
                {'name': 'inertia', 'params': {'length': 20, 'rvi_length': 14}},
                {'name': 'kdj', 'params': {'length': 9, 'signal': 3}},
                {'name': 'kst', 'params': {'roc1': 10, 'roc2': 15, 'roc3': 20, 'roc4': 30, 'sma1': 10, 'sma2': 10, 'sma3': 10, 'sma4': 15}},
                {'name': 'pgo', 'params': {'length': 14}},
                {'name': 'psl', 'params': {'length': 12}},
                {'name': 'qqe', 'params': {'length': 14, 'smooth': 5}},
                {'name': 'rsx', 'params': {'length': 14}},
                {'name': 'rvgi', 'params': {'length': 14, 'swma_length': 4}},
                {'name': 'slope', 'params': {'length': 1}},
                {'name': 'smi', 'params': {'fast': 5, 'slow': 20, 'signal': 5}},
                {'name': 'squeeze', 'params': {'bb_length': 20, 'bb_std': 2, 'kc_length': 20, 'kc_scalar': 1.5}},
                {'name': 'squeeze_pro', 'params': {'bb_length': 20, 'bb_std': 2, 'kc_length': 20, 'kc_scalar_wide': 2, 'kc_scalar_normal': 1.5, 'kc_scalar_narrow': 1}},
                {'name': 'stc', 'params': {'tclength': 10, 'fast': 23, 'slow': 50}},
                {'name': 'td_seq', 'params': {}},
                {'name': 'trix', 'params': {'length': 14, 'signal': 9}},
                {'name': 'tsi', 'params': {'fast': 25, 'slow': 13, 'signal': 13}},
                {'name': 'uo', 'params': {'fast': 7, 'medium': 14, 'slow': 28}},
            ],
            'volatility': [
                # Volatility Indicators
                {'name': 'bbands', 'params': {'length': 20, 'std': 2}},
                {'name': 'atr', 'params': {'length': 14}},
                {'name': 'natr', 'params': {'length': 14}},
                {'name': 'kc', 'params': {'length': 20}},
                {'name': 'donchian', 'params': {'lower_length': 20, 'upper_length': 20}},
                {'name': 'aberration', 'params': {'length': 5}},
                {'name': 'accbands', 'params': {'length': 20}},
                {'name': 'massi', 'params': {'fast': 9, 'slow': 25}},
                {'name': 'pdist', 'params': {}},
                {'name': 'rvi', 'params': {'length': 14}},
                {'name': 'thermo', 'params': {'length': 20}},
                {'name': 'true_range', 'params': {}},
                {'name': 'ui', 'params': {'length': 14}},
            ],
            'volume': [
                # Volume Indicators
                {'name': 'obv', 'params': {}},
                {'name': 'ad', 'params': {}},
                {'name': 'adosc', 'params': {'fast': 3, 'slow': 10}},
                {'name': 'cmf', 'params': {'length': 20}},
                {'name': 'mfi', 'params': {'length': 14}},
                {'name': 'vwap', 'params': {}},
                {'name': 'pvt', 'params': {}},
                {'name': 'aobv', 'params': {'fast': 4, 'slow': 12}},
                {'name': 'efi', 'params': {'length': 13}},
                {'name': 'eom', 'params': {'length': 14}},
                {'name': 'kvo', 'params': {'fast': 34, 'slow': 55}},
                {'name': 'nvi', 'params': {'length': 255}},
                {'name': 'pvi', 'params': {'length': 255}},
                {'name': 'pvo', 'params': {'fast': 12, 'slow': 26}},
                {'name': 'pvol', 'params': {}},
                {'name': 'pvr', 'params': {}},
                {'name': 'vp', 'params': {'width': 10}},
            ],
            'trend': [
                # Trend Indicators
                {'name': 'adx', 'params': {'length': 14}},
                {'name': 'aroon', 'params': {'length': 14}},
                {'name': 'psar', 'params': {}},
                {'name': 'amat', 'params': {'fast': 8, 'slow': 21}},
                {'name': 'chop', 'params': {'length': 14}},
                {'name': 'cksp', 'params': {'p': 10, 'x': 1, 'q': 9}},
                {'name': 'decay', 'params': {'length': 5}},
                {'name': 'decreasing', 'params': {'length': 1}},
                {'name': 'dpo', 'params': {'length': 20}},
                {'name': 'increasing', 'params': {'length': 1}},
                {'name': 'long_run', 'params': {'fast': 2, 'slow': 5}},
                {'name': 'qstick', 'params': {'length': 10}},
                {'name': 'short_run', 'params': {'fast': 2, 'slow': 5}},
                {'name': 'ttm_trend', 'params': {'length': 6}},
                {'name': 'vhf', 'params': {'length': 28}},
                {'name': 'vortex', 'params': {'length': 14}},
            ],
            'statistics': [
                # Statistical Indicators
                {'name': 'zscore', 'params': {'length': 20}},
                {'name': 'stdev', 'params': {'length': 20}},
                {'name': 'variance', 'params': {'length': 20}},
                {'name': 'skew', 'params': {'length': 20}},
                {'name': 'kurtosis', 'params': {'length': 20}},
                {'name': 'entropy', 'params': {'length': 10}},
                {'name': 'mad', 'params': {'length': 30}},
                {'name': 'median', 'params': {'length': 30}},
                {'name': 'quantile', 'params': {'length': 30}},
            ],
            'performance': [
                # Performance Indicators
                {'name': 'log_return', 'params': {'length': 1}},
                {'name': 'percent_return', 'params': {'length': 1}},
                {'name': 'log_return', 'params': {'length': 1, 'cumulative': True}},
                {'name': 'percent_return', 'params': {'length': 1, 'cumulative': True}},
                {'name': 'drawdown', 'params': {}},
            ],
            'cycles': [
                # Cycle Indicators (only ebsw is available in this pandas-ta version)
                {'name': 'ebsw', 'params': {'length': 40}},
            ],
            'candles': [
                # Candlestick Pattern Recognition
                {'name': 'cdl_pattern', 'params': {'name': 'all'}},
                {'name': 'cdl_z', 'params': {}},
                {'name': 'ha', 'params': {}},
            ]
        }
    
    def get_market_data(self, ticker: str, exchange: str, date: str, 
                       start_time: str = "09:15", end_time: str = "15:30") -> pd.DataFrame:
        """
        Get market data for analysis
        
        Args:
            ticker: Stock ticker symbol
            exchange: Exchange name
            date: Date in DD-MM-YYYY format
            start_time: Start time in HH:MM format
            end_time: End time in HH:MM format
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            # Use the backtester to get data
            backtester = SmartVectorizedBacktester(
                ticker=ticker,
                exchange=exchange, 
                start=start_time,
                end=end_time,
                date=date,
                tokenid=None  # Will be fetched automatically
            )
            
            # Get the data
            data = backtester._fetch_and_process_data()
            
            if data is None or data.empty:
                logger.error(f"❌ No data available for {ticker} on {date}")
                return pd.DataFrame()
            
            # Ensure proper column names for pandas-ta
            required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            if 'volume' in data.columns:
                data = data.rename(columns={'volume': 'Volume'})
            
            # Verify all required columns exist
            missing_cols = [col for col in required_columns if col not in data.columns]
            if missing_cols:
                logger.error(f"❌ Missing required columns: {missing_cols}")
                return pd.DataFrame()
            
            logger.info(f"✅ Retrieved {len(data)} candles for {ticker}")
            return data
            
        except Exception as e:
            logger.error(f"❌ Error getting market data: {str(e)}")
            return pd.DataFrame()
    
    def analyze_signals_from_backtester(self, ticker: str, exchange: str, date: str,
                                      method: str = 'extension', include_history: bool = True,
                                      categories: List[str] = None) -> Dict:
        """
        Analyze technical indicators at signal candles from backtester
        
        Args:
            ticker: Stock ticker symbol
            exchange: Exchange name  
            date: Date in DD-MM-YYYY format
            method: Analysis method to use
            include_history: Include 2 minutes of historical data before signals
            
        Returns:
            Dictionary with analysis results
        """
        try:
            logger.info(f"🔍 Analyzing signals for {ticker} on {date}")
            
            # Get signals from backtester
            backtester = SmartVectorizedBacktester(
                ticker=ticker,
                exchange=exchange,
                start="09:15", 
                end="15:30",
                date=date,
                tokenid=None
            )
            
            # Run backtester to get signals
            results = backtester.run_analysis()
            signals = results.get('signals', [])
            
            if not signals:
                logger.warning(f"⚠️ No signals found for {ticker} on {date}")
                return {'ticker': ticker, 'date': date, 'signals': [], 'analysis': {}}
            
            logger.info(f"📊 Found {len(signals)} signals")
            
            # Get market data
            market_data = self.get_market_data(ticker, exchange, date)
            if market_data.empty:
                return {'ticker': ticker, 'date': date, 'signals': [], 'analysis': {}}
            
            # Analyze indicators at each signal
            signal_analysis = []
            
            for signal in signals:
                signal_time = signal['time']
                signal_type = signal['signal_type']
                
                logger.info(f"🎯 Analyzing {signal_type} signal at {signal_time}")
                
                # Get the specific candle and history
                candle_analysis = self._analyze_specific_candle(
                    market_data, signal_time, method, include_history, categories
                )
                
                candle_analysis.update({
                    'signal_time': signal_time,
                    'signal_type': signal_type,
                    'signal_reason': signal.get('reason', '')
                })
                
                signal_analysis.append(candle_analysis)
            
            return {
                'ticker': ticker,
                'date': date, 
                'method': method,
                'signals': signals,
                'analysis': signal_analysis,
                'total_signals': len(signals)
            }
            
        except Exception as e:
            logger.error(f"❌ Error analyzing signals: {str(e)}")
            return {'ticker': ticker, 'date': date, 'signals': [], 'analysis': {}}
    
    def analyze_specific_candles(self, ticker: str, exchange: str, date: str,
                               candle_times: List[str], method: str = 'extension',
                               include_history: bool = True, categories: List[str] = None) -> Dict:
        """
        Analyze technical indicators at specific candle times
        
        Args:
            ticker: Stock ticker symbol
            exchange: Exchange name
            date: Date in DD-MM-YYYY format  
            candle_times: List of times in HH:MM format
            method: Analysis method to use
            include_history: Include 2 minutes of historical data
            
        Returns:
            Dictionary with analysis results
        """
        try:
            logger.info(f"🔍 Analyzing specific candles for {ticker} on {date}")
            logger.info(f"🕐 Candle times: {', '.join(candle_times)}")
            
            # Get market data
            market_data = self.get_market_data(ticker, exchange, date)
            if market_data.empty:
                return {'ticker': ticker, 'date': date, 'candles': [], 'analysis': {}}
            
            # Analyze each specified candle
            candle_analysis = []
            
            for candle_time in candle_times:
                logger.info(f"🎯 Analyzing candle at {candle_time}")
                
                analysis = self._analyze_specific_candle(
                    market_data, candle_time, method, include_history, categories
                )
                
                analysis.update({
                    'candle_time': candle_time
                })
                
                candle_analysis.append(analysis)
            
            return {
                'ticker': ticker,
                'date': date,
                'method': method, 
                'candle_times': candle_times,
                'analysis': candle_analysis,
                'total_candles': len(candle_times)
            }
            
        except Exception as e:
            logger.error(f"❌ Error analyzing specific candles: {str(e)}")
            return {'ticker': ticker, 'date': date, 'candles': [], 'analysis': {}}

    def analyze_time_period(self, ticker: str, exchange: str, date: str,
                          start_time: str, end_time: str, method: str = 'extension',
                          categories: List[str] = None) -> Dict:
        """
        Analyze technical indicators for a specific time period

        Args:
            ticker: Stock ticker symbol
            exchange: Exchange name
            date: Date in DD-MM-YYYY format
            start_time: Start time in HH:MM format
            end_time: End time in HH:MM format
            method: Analysis method to use

        Returns:
            Dictionary with analysis results
        """
        try:
            logger.info(f"🔍 Analyzing time period {start_time}-{end_time} for {ticker} on {date}")

            # Get market data for the specific period
            market_data = self.get_market_data(ticker, exchange, date, start_time, end_time)
            if market_data.empty:
                return {'ticker': ticker, 'date': date, 'period': f"{start_time}-{end_time}", 'analysis': {}}

            # Analyze the entire period
            analysis = self._analyze_dataframe(market_data, method, categories)

            return {
                'ticker': ticker,
                'date': date,
                'method': method,
                'period': f"{start_time}-{end_time}",
                'data_points': len(market_data),
                'analysis': analysis
            }

        except Exception as e:
            logger.error(f"❌ Error analyzing time period: {str(e)}")
            return {'ticker': ticker, 'date': date, 'period': f"{start_time}-{end_time}", 'analysis': {}}

    def analyze_full_market_session(self, ticker: str, exchange: str, date: str,
                                  method: str = 'extension', categories: List[str] = None) -> Dict:
        """
        Analyze technical indicators for complete market session

        Args:
            ticker: Stock ticker symbol
            exchange: Exchange name
            date: Date in DD-MM-YYYY format
            method: Analysis method to use

        Returns:
            Dictionary with analysis results
        """
        try:
            logger.info(f"🔍 Analyzing full market session for {ticker} on {date}")

            # Get full market data (09:15 to 15:30)
            market_data = self.get_market_data(ticker, exchange, date, "09:15", "15:30")
            if market_data.empty:
                return {'ticker': ticker, 'date': date, 'session': 'full', 'analysis': {}}

            # Analyze the entire session
            analysis = self._analyze_dataframe(market_data, method, categories)

            return {
                'ticker': ticker,
                'date': date,
                'method': method,
                'session': 'full_market',
                'session_period': '09:15-15:30',
                'data_points': len(market_data),
                'analysis': analysis
            }

        except Exception as e:
            logger.error(f"❌ Error analyzing full market session: {str(e)}")
            return {'ticker': ticker, 'date': date, 'session': 'full', 'analysis': {}}

    def _analyze_specific_candle(self, market_data: pd.DataFrame, target_time: str,
                               method: str, include_history: bool = True,
                               categories: List[str] = None) -> Dict:
        """
        Analyze technical indicators at a specific candle with optional history

        Args:
            market_data: DataFrame with OHLCV data
            target_time: Target time in HH:MM format
            method: Analysis method to use
            include_history: Include 2 minutes of historical data

        Returns:
            Dictionary with analysis results
        """
        try:
            # Find the target candle
            target_candle = None
            target_index = None

            for idx, row in market_data.iterrows():
                candle_time = idx.strftime('%H:%M')
                if candle_time == target_time:
                    target_candle = row
                    target_index = idx
                    break

            if target_candle is None:
                logger.warning(f"⚠️ Candle at {target_time} not found")
                return {'error': f'Candle at {target_time} not found'}

            # Prepare data for analysis
            if include_history:
                # Get data up to and including the target candle
                data_for_analysis = market_data.loc[:target_index].copy()

                # Ensure we have enough data for meaningful analysis
                if len(data_for_analysis) < 3:
                    logger.warning(f"⚠️ Insufficient data for analysis at {target_time}")
                    return {'error': f'Insufficient data at {target_time}'}
            else:
                # Use only the target candle (limited analysis possible)
                data_for_analysis = market_data.loc[target_index:target_index].copy()

            # Perform the analysis
            analysis = self._analyze_dataframe(data_for_analysis, method, categories)

            # Add specific candle information
            analysis.update({
                'target_candle': {
                    'time': target_time,
                    'open': float(target_candle['Open']),
                    'high': float(target_candle['High']),
                    'low': float(target_candle['Low']),
                    'close': float(target_candle['Close']),
                    'volume': float(target_candle['Volume'])
                },
                'data_points_used': len(data_for_analysis),
                'include_history': include_history
            })

            return analysis

        except Exception as e:
            logger.error(f"❌ Error analyzing specific candle: {str(e)}")
            return {'error': str(e)}

    def _analyze_dataframe(self, data: pd.DataFrame, method: str, categories: List[str] = None) -> Dict:
        """
        Analyze DataFrame using specified method

        Args:
            data: DataFrame with OHLCV data
            method: Analysis method to use
            categories: List of categories to include (None = all categories)

        Returns:
            Dictionary with analysis results
        """
        try:
            if data.empty:
                return {'error': 'Empty data provided'}

            logger.info(f"📊 Analyzing {len(data)} candles using {method} method")
            if categories:
                logger.info(f"📂 Including categories: {', '.join(categories)}")

            # Dispatch to appropriate method
            if method == 'direct_call':
                return self._analyze_direct_call_method(data, categories)
            elif method == 'extension':
                return self._analyze_extension_method(data, categories)
            elif method == 'extension_kind':
                return self._analyze_extension_kind_method(data, categories)
            elif method == 'strategy_all':
                return self._analyze_strategy_all(data)
            elif method == 'strategy_all_automated':
                return self._analyze_strategy_all_automated(data)
            elif method == 'strategy_common':
                return self._analyze_strategy_common(data)
            elif method == 'strategy_category':
                return self._analyze_strategy_category(data, categories)
            elif method == 'custom_strategy':
                return self._analyze_custom_strategy(data)
            # Legacy method names for backward compatibility
            elif method == 'standard':
                return self._analyze_direct_call_method(data, categories)
            elif method == 'study':
                return self._analyze_extension_method(data, categories)
            else:
                logger.error(f"❌ Unknown method: {method}")
                return {'error': f'Unknown method: {method}'}

        except Exception as e:
            logger.error(f"❌ Error in dataframe analysis: {str(e)}")
            return {'error': str(e)}

    def _analyze_direct_call_method(self, data: pd.DataFrame, categories: List[str] = None) -> Dict:
        """
        Analyze using direct function calls (ta.indicator_name)

        This method calls each indicator directly using ta.indicator_name() syntax
        """
        try:
            logger.info("🔧 Using Direct Call Method")
            results = {}

            # Extract OHLCV series
            open_series = data['Open']
            high_series = data['High']
            low_series = data['Low']
            close_series = data['Close']
            volume_series = data['Volume']

            # Determine which categories to process
            categories_to_process = categories if categories else self.categories

            # Process each category
            for category in categories_to_process:
                if category not in self.category_indicators:
                    logger.warning(f"⚠️ Unknown category: {category}")
                    continue

                logger.info(f"📂 Processing {category} indicators...")
                category_results = {}

                for indicator_def in self.category_indicators[category]:
                    indicator_name = indicator_def['name']
                    params = indicator_def['params']

                    try:
                        # Get the indicator function
                        if hasattr(ta, indicator_name):
                            indicator_func = getattr(ta, indicator_name)

                            # Prepare parameters based on indicator requirements
                            call_params = self._prepare_direct_call_params(
                                indicator_name, params, open_series, high_series,
                                low_series, close_series, volume_series
                            )

                            # Call the indicator
                            result = indicator_func(**call_params)

                            # Process the result
                            processed_result = self._process_indicator_result(
                                result, f"{indicator_name}_{self._params_to_suffix(params)}"
                            )

                            if processed_result:
                                category_results.update(processed_result)

                        else:
                            logger.warning(f"⚠️ Indicator {indicator_name} not found")

                    except Exception as e:
                        logger.warning(f"⚠️ Error calculating {indicator_name}: {str(e)}")

                if category_results:
                    results[category] = category_results
                    logger.info(f"✅ {category}: {len(category_results)} indicators calculated")

            # Flatten results for easier access
            flattened_results = {}
            for category, indicators in results.items():
                for indicator_name, value in indicators.items():
                    flattened_results[f"{category}_{indicator_name}"] = value

            return {
                'method': 'direct_call',
                'indicators': flattened_results,
                'by_category': results,
                'total_indicators': len(flattened_results),
                'categories_processed': list(results.keys())
            }

        except Exception as e:
            logger.error(f"❌ Error in direct call method: {str(e)}")
            return {'error': str(e)}

    def _prepare_direct_call_params(self, indicator_name: str, params: Dict,
                                  open_series: pd.Series, high_series: pd.Series,
                                  low_series: pd.Series, close_series: pd.Series,
                                  volume_series: pd.Series) -> Dict:
        """Prepare parameters for direct indicator calls based on documentation"""
        call_params = params.copy()

        # Overlap indicators (require close)
        overlap_close_only = [
            'sma', 'ema', 'wma', 'hma', 'alma', 'dema', 'tema', 'trima',
            'kama', 'fwma', 'jma', 'linreg', 'midpoint',
            'pwma', 'rma', 'sinwma', 'ssf', 'swma', 't3', 'vidya', 'zlma'
        ]

        overlap_cv = ['vwma']  # Close + Volume
        overlap_hl = ['midprice']  # High + Low

        # Momentum indicators (mostly require close, some need HLC)
        momentum_close_only = [
            'rsi', 'macd', 'ppo', 'apo', 'cfo', 'cg', 'cmo', 'coppock', 'cti',
            'er', 'inertia', 'psl', 'qqe', 'rsx', 'slope', 'trix', 'tsi', 'roc', 'mom'
        ]

        momentum_hlc = [
            'stoch', 'stochrsi', 'cci', 'willr', 'ao', 'kdj', 'smi', 'uo',
            'eri', 'fisher', 'pgo', 'squeeze', 'squeeze_pro'
        ]

        momentum_ohlc = [
            'bop', 'rvgi'
        ]

        # Volatility indicators (require HLC)
        volatility_hlc = [
            'atr', 'natr', 'bbands', 'kc', 'donchian', 'aberration', 'accbands',
            'rvi', 'thermo', 'true_range', 'ui', 'supertrend'
        ]

        # Volatility indicators (require HL only)
        volatility_hl = ['massi']

        # Volatility indicators (require OHLC)
        volatility_ohlc = ['pdist']

        # Trend indicators (require HLC)
        trend_hlc = [
            'adx', 'aroon', 'psar', 'amat', 'chop', 'cksp', 'dpo', 'vhf',
            'vortex', 'ttm_trend'
        ]

        # Trend indicators (require open_ + close)
        trend_oc = ['qstick']

        # Volume indicators
        volume_cv = ['obv', 'pvt', 'vp']  # Close + Volume
        volume_hlcv = ['ad', 'adosc', 'cmf', 'mfi', 'eom', 'kvo', 'vwap']  # High + Low + Close + Volume
        volume_cv_only = ['aobv', 'efi', 'nvi', 'pvi', 'pvo', 'pvol', 'pvr']  # Close + Volume

        # Statistics indicators (require close)
        statistics_indicators = [
            'zscore', 'stdev', 'variance', 'skew', 'kurtosis', 'entropy',
            'mad', 'median', 'quantile'
        ]

        # Performance indicators (require close)
        performance_indicators = ['log_return', 'percent_return', 'drawdown']

        # Cycles indicators (require close)
        cycles_indicators = ['ebsw']

        # Candle indicators (require OHLC with open_)
        candle_indicators = ['cdl_pattern', 'cdl_z', 'ha']

        # Special cases
        special_cases = ['decay', 'decreasing', 'increasing', 'long_run', 'short_run']

        # Assign parameters based on indicator type
        if indicator_name in overlap_close_only:
            call_params['close'] = close_series
        elif indicator_name in overlap_cv:
            call_params['close'] = close_series
            call_params['volume'] = volume_series
        elif indicator_name in overlap_hl:
            call_params['high'] = high_series
            call_params['low'] = low_series
        elif indicator_name in momentum_close_only:
            call_params['close'] = close_series
        elif indicator_name in momentum_hlc:
            call_params['high'] = high_series
            call_params['low'] = low_series
            call_params['close'] = close_series
        elif indicator_name in momentum_ohlc:
            call_params['open_'] = open_series
            call_params['high'] = high_series
            call_params['low'] = low_series
            call_params['close'] = close_series
        elif indicator_name in volatility_hlc:
            call_params['high'] = high_series
            call_params['low'] = low_series
            call_params['close'] = close_series
        elif indicator_name in volatility_hl:
            call_params['high'] = high_series
            call_params['low'] = low_series
        elif indicator_name in volatility_ohlc:
            call_params['open_'] = open_series
            call_params['high'] = high_series
            call_params['low'] = low_series
            call_params['close'] = close_series
        elif indicator_name in trend_hlc:
            call_params['high'] = high_series
            call_params['low'] = low_series
            call_params['close'] = close_series
        elif indicator_name in trend_oc:
            call_params['open_'] = open_series
            call_params['close'] = close_series
        elif indicator_name in volume_cv:
            call_params['close'] = close_series
            call_params['volume'] = volume_series
        elif indicator_name in volume_hlcv:
            call_params['high'] = high_series
            call_params['low'] = low_series
            call_params['close'] = close_series
            call_params['volume'] = volume_series
        elif indicator_name in volume_cv_only:
            call_params['close'] = close_series
            call_params['volume'] = volume_series
        elif indicator_name in statistics_indicators:
            call_params['close'] = close_series
        elif indicator_name in performance_indicators:
            call_params['close'] = close_series
        elif indicator_name in cycles_indicators:
            call_params['close'] = close_series
        elif indicator_name in candle_indicators:
            call_params['open'] = open_series
            call_params['high'] = high_series
            call_params['low'] = low_series
            call_params['close'] = close_series
        elif indicator_name in special_cases:
            call_params['close'] = close_series
        else:
            # Default: try close series
            call_params['close'] = close_series

        return call_params

    def _params_to_suffix(self, params: Dict) -> str:
        """Convert parameters to suffix string"""
        if not params:
            return ""

        suffix_parts = []
        for key, value in params.items():
            if key not in ['close', 'high', 'low', 'open', 'volume']:
                suffix_parts.append(str(value))

        return "_".join(suffix_parts) if suffix_parts else ""

    def _process_indicator_result(self, result, base_name: str) -> Dict:
        """Process indicator result and return flattened dictionary"""
        if result is None:
            return {}

        processed = {}

        if isinstance(result, pd.DataFrame):
            # Multi-column indicators
            for col in result.columns:
                latest_val = result[col].iloc[-1] if not result[col].empty else None
                if pd.notna(latest_val):
                    processed[f"{base_name}_{col}"] = float(latest_val)
        elif isinstance(result, pd.Series):
            # Single-column indicators
            latest_val = result.iloc[-1] if not result.empty else None
            if pd.notna(latest_val):
                processed[base_name] = float(latest_val)

        return processed

    def _analyze_extension_method(self, data: pd.DataFrame, categories: List[str] = None) -> Dict:
        """
        Analyze using DataFrame ta extension convention (df.ta.indicator_name)
        """
        try:
            logger.info("🔧 Using Extension Method")

            # Create a copy to avoid modifying original data
            df = data.copy()

            # Determine which categories to process
            categories_to_process = categories if categories else self.categories

            results_by_category = {}

            # Process each category
            for category in categories_to_process:
                if category not in self.category_indicators:
                    logger.warning(f"⚠️ Unknown category: {category}")
                    continue

                logger.info(f"📂 Processing {category} indicators...")
                category_count = 0

                for indicator_def in self.category_indicators[category]:
                    indicator_name = indicator_def['name']
                    params = indicator_def['params']

                    try:
                        # Check if the indicator method exists
                        if hasattr(df.ta, indicator_name):
                            indicator_method = getattr(df.ta, indicator_name)

                            # Call the indicator with append=True
                            indicator_method(append=True, **params)
                            category_count += 1

                        else:
                            logger.warning(f"⚠️ Extension method {indicator_name} not found")

                    except Exception as e:
                        logger.warning(f"⚠️ Error calculating {indicator_name}: {str(e)}")

                if category_count > 0:
                    results_by_category[category] = category_count
                    logger.info(f"✅ {category}: {category_count} indicators calculated")

            # Extract latest values from all new columns
            original_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            indicator_columns = [col for col in df.columns if col not in original_columns]

            results = {}
            nan_count = 0
            for col in indicator_columns:
                latest_val = df[col].iloc[-1] if not df[col].empty else None
                if pd.notna(latest_val):
                    results[col] = float(latest_val)
                else:
                    nan_count += 1
                    # Include NaN values as 'N/A' so users can see all indicators
                    results[col] = 'N/A'

            logger.debug(f"📊 Extension method: {len(indicator_columns)} total columns, {len(results)-nan_count} valid values, {nan_count} NaN values")

            return {
                'method': 'extension',
                'indicators': results,
                'by_category': results_by_category,
                'total_indicators': len(results),
                'categories_processed': list(results_by_category.keys())
            }

        except Exception as e:
            logger.error(f"❌ Error in extension method: {str(e)}")
            return {'error': str(e)}

    def _analyze_extension_kind_method(self, data: pd.DataFrame, categories: List[str] = None) -> Dict:
        """
        Analyze using DataFrame extension with kind parameter (df.ta(kind="indicator"))
        """
        try:
            logger.info("🔧 Using Extension Kind Method")

            # Create a copy to avoid modifying original data
            df = data.copy()

            # Determine which categories to process
            categories_to_process = categories if categories else self.categories

            results_by_category = {}

            # Process each category
            for category in categories_to_process:
                if category not in self.category_indicators:
                    logger.warning(f"⚠️ Unknown category: {category}")
                    continue

                logger.info(f"📂 Processing {category} indicators...")
                category_count = 0

                for indicator_def in self.category_indicators[category]:
                    indicator_name = indicator_def['name']
                    params = indicator_def['params']

                    try:
                        # Use the kind parameter approach
                        result = df.ta(kind=indicator_name, **params)

                        if result is not None:
                            # Append result to dataframe
                            if isinstance(result, pd.DataFrame):
                                for col in result.columns:
                                    df[col] = result[col]
                            elif isinstance(result, pd.Series):
                                df[result.name] = result

                            category_count += 1

                    except Exception as e:
                        logger.warning(f"⚠️ Error calculating {indicator_name}: {str(e)}")

                if category_count > 0:
                    results_by_category[category] = category_count
                    logger.info(f"✅ {category}: {category_count} indicators calculated")

            # Extract latest values from all new columns
            original_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            indicator_columns = [col for col in df.columns if col not in original_columns]

            results = {}
            for col in indicator_columns:
                latest_val = df[col].iloc[-1] if not df[col].empty else None
                if pd.notna(latest_val):
                    results[col] = float(latest_val)

            return {
                'method': 'extension_kind',
                'indicators': results,
                'by_category': results_by_category,
                'total_indicators': len(results),
                'categories_processed': list(results_by_category.keys())
            }

        except Exception as e:
            logger.error(f"❌ Error in extension kind method: {str(e)}")
            return {'error': str(e)}

    def _analyze_study_method(self, data: pd.DataFrame) -> Dict:
        """
        Analyze using comprehensive manual approach (Study not available in this version)
        """
        try:
            logger.info("🔧 Using Comprehensive Manual Method (Study alternative)")

            # Create a copy to avoid modifying original data
            df = data.copy()

            # Check if Study is available
            if hasattr(ta, 'Study'):
                # Use Study if available
                comprehensive_study = ta.Study(
                    name="Comprehensive Analysis",
                    description="Complete technical analysis for signal detection",
                    cores=0,
                    ta=[
                        {"kind": "sma", "length": 10},
                        {"kind": "sma", "length": 20},
                        {"kind": "ema", "length": 12},
                        {"kind": "rsi", "length": 14},
                        {"kind": "macd"},
                        {"kind": "bbands", "length": 20},
                        {"kind": "atr", "length": 14},
                        {"kind": "adx", "length": 14}
                    ]
                )
                df.ta.study(comprehensive_study, verbose=False)
            else:
                # Manual comprehensive analysis
                logger.info("📝 Study not available, using manual comprehensive analysis")

                # Apply indicators manually using extension method
                try:
                    # Moving Averages
                    df.ta.sma(length=5, append=True)
                    df.ta.sma(length=10, append=True)
                    df.ta.sma(length=20, append=True)
                    df.ta.sma(length=50, append=True)
                    df.ta.ema(length=5, append=True)
                    df.ta.ema(length=10, append=True)
                    df.ta.ema(length=20, append=True)
                    df.ta.ema(length=50, append=True)

                    # Momentum Indicators
                    df.ta.rsi(length=14, append=True)
                    df.ta.rsi(length=21, append=True)
                    df.ta.macd(append=True)
                    df.ta.stoch(append=True)
                    df.ta.cci(length=20, append=True)
                    df.ta.willr(length=14, append=True)
                    df.ta.roc(length=10, append=True)
                    df.ta.mom(length=10, append=True)

                    # Volatility Indicators
                    df.ta.bbands(length=20, append=True)
                    df.ta.atr(length=14, append=True)

                    # Volume Indicators
                    df.ta.obv(append=True)
                    df.ta.ad(append=True)
                    df.ta.mfi(length=14, append=True)

                    # Trend Indicators
                    df.ta.adx(length=14, append=True)
                    df.ta.psar(append=True)

                except Exception as indicator_error:
                    logger.warning(f"⚠️ Some indicators failed: {str(indicator_error)}")

            # Extract latest values
            results = {}
            indicator_columns = [col for col in df.columns if col not in ['Open', 'High', 'Low', 'Close', 'Volume']]

            for col in indicator_columns:
                latest_val = df[col].iloc[-1] if not df[col].empty else None
                if pd.notna(latest_val):
                    results[col] = float(latest_val)

            return {
                'method': 'study_manual',
                'indicators': results,
                'total_indicators': len(results),
                'note': 'Manual comprehensive analysis (Study not available)'
            }

        except Exception as e:
            logger.error(f"❌ Error in study method: {str(e)}")
            return {'error': str(e)}

    def _analyze_strategy_all(self, data: pd.DataFrame) -> Dict:
        """
        Analyze using AllStrategy (all available indicators) - Proper Implementation

        This implements the correct approach as shown in pandas-ta notebook:
        df = df.ta.ticker("symbol")
        df.ta.cores = 0
        df.ta.strategy("All", timed=True)
        """
        try:
            logger.info("🔧 Using AllStrategy (All Indicators) - Proper Implementation")

            # Create a copy to avoid modifying original data
            df = data.copy()

            # Set up DataFrame for pandas-ta as shown in notebook
            try:
                # Set cores to 0 to disable multiprocessing (for stability)
                df.ta.cores = 0

                # Apply All strategy with timing as shown in notebook
                logger.info("📊 Applying All strategy with 285+ indicators...")

                # Suppress pandas FutureWarnings about Series.append deprecation
                import warnings
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", category=FutureWarning, message=".*append.*")
                    warnings.filterwarnings("ignore", category=UserWarning, message=".*timezone.*")
                    df.ta.strategy("All", timed=True)

                logger.info("✅ All strategy applied successfully")

            except Exception as strategy_error:
                logger.warning(f"⚠️ Strategy All failed, trying fallback: {str(strategy_error)}")

                # Fallback: Try without ticker and timing
                try:
                    df.ta.cores = 0
                    with warnings.catch_warnings():
                        warnings.filterwarnings("ignore", category=FutureWarning, message=".*append.*")
                        warnings.filterwarnings("ignore", category=UserWarning, message=".*timezone.*")
                        df.ta.strategy("All")
                except Exception as fallback_error:
                    logger.error(f"❌ Fallback also failed: {str(fallback_error)}")
                    return {'error': f'AllStrategy failed: {str(strategy_error)}'}

            # Extract latest values from all new columns
            original_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            indicator_columns = [col for col in df.columns if col not in original_columns]

            logger.info(f"📊 Found {len(indicator_columns)} indicator columns")

            results = {}
            for col in indicator_columns:
                try:
                    latest_val = df[col].iloc[-1] if not df[col].empty else None
                    if pd.notna(latest_val):
                        results[col] = float(latest_val)
                except Exception as col_error:
                    logger.warning(f"⚠️ Error processing column {col}: {str(col_error)}")

            return {
                'method': 'strategy_all',
                'indicators': results,
                'total_indicators': len(results),
                'total_columns': len(indicator_columns),
                'strategy': 'AllStrategy (proper implementation)',
                'note': 'Uses df.ta.strategy("All", timed=True) as per pandas-ta notebook'
            }

        except Exception as e:
            logger.error(f"❌ Error in AllStrategy method: {str(e)}")
            return {'error': str(e)}

    def _analyze_strategy_all_automated(self, data: pd.DataFrame) -> Dict:
        """
        Automated All Indicators Analysis with optimized settings

        Uses df.ta.cores = 6 and df.ta.strategy("All", timed=True) for maximum performance
        and comprehensive analysis with 280+ technical indicators.

        This method is specifically designed for automated fetching of all available
        indicators with optimal performance settings.
        """
        try:
            logger.info("🚀 Using Automated All Indicators Analysis (cores=6, timed=True)")
            logger.info("📊 This will calculate 280+ technical indicators with optimized performance")

            # Create a copy to avoid modifying original data
            df = data.copy()

            # Ensure we have the required OHLCV columns
            required_columns = ['Open', 'High', 'Low', 'Close']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                logger.error(f"❌ Missing required columns: {missing_columns}")
                return {'error': f'Missing required columns: {missing_columns}'}

            # Set up DataFrame for pandas-ta with optimized settings
            try:
                # Set cores to 6 for optimal performance as requested
                df.ta.cores = 6
                logger.info("⚙️ Set cores to 6 for optimal performance")

                # Apply All strategy with timing for comprehensive analysis
                logger.info("📊 Applying All strategy with 280+ indicators (cores=6, timed=True)...")

                # Suppress warnings for cleaner output
                import warnings
                with warnings.catch_warnings():
                    warnings.filterwarnings("ignore", category=FutureWarning, message=".*append.*")
                    warnings.filterwarnings("ignore", category=UserWarning, message=".*timezone.*")
                    warnings.filterwarnings("ignore", category=RuntimeWarning)

                    # Apply the automated all strategy with timing
                    start_time = pd.Timestamp.now()
                    df.ta.strategy("All", timed=True)
                    end_time = pd.Timestamp.now()

                    calculation_time = (end_time - start_time).total_seconds()
                    logger.info(f"✅ All strategy completed in {calculation_time:.2f} seconds")

            except Exception as strategy_error:
                logger.warning(f"⚠️ Automated strategy failed, trying fallback: {str(strategy_error)}")

                # Fallback: Try with cores=4 and without timing
                try:
                    df.ta.cores = 4
                    logger.info("⚙️ Fallback: Using cores=4")
                    with warnings.catch_warnings():
                        warnings.filterwarnings("ignore", category=FutureWarning, message=".*append.*")
                        warnings.filterwarnings("ignore", category=UserWarning, message=".*timezone.*")
                        warnings.filterwarnings("ignore", category=RuntimeWarning)
                        df.ta.strategy("All", timed=True)
                        logger.info("✅ Fallback strategy completed")
                except Exception as fallback_error:
                    logger.warning(f"⚠️ Fallback with cores=4 failed, trying cores=0: {str(fallback_error)}")

                    # Final fallback: cores=0 (single-threaded)
                    try:
                        df.ta.cores = 0
                        logger.info("⚙️ Final fallback: Using cores=0 (single-threaded)")
                        with warnings.catch_warnings():
                            warnings.filterwarnings("ignore", category=FutureWarning, message=".*append.*")
                            warnings.filterwarnings("ignore", category=UserWarning, message=".*timezone.*")
                            warnings.filterwarnings("ignore", category=RuntimeWarning)
                            df.ta.strategy("All")
                            logger.info("✅ Final fallback strategy completed")
                    except Exception as final_error:
                        logger.error(f"❌ All fallback attempts failed: {str(final_error)}")
                        return {'error': f'Automated All strategy failed: {str(strategy_error)}'}

            # Get original columns to identify new indicator columns
            original_columns = set(data.columns)
            all_columns = set(df.columns)
            indicator_columns = list(all_columns - original_columns)

            logger.info(f"📊 Generated {len(indicator_columns)} new indicator columns")

            # Extract latest values from all new indicator columns
            results = {}
            processed_indicators = 0

            for col in indicator_columns:
                try:
                    if col in df.columns:
                        # Get the last non-null value
                        series = df[col].dropna()
                        if len(series) > 0:
                            latest_val = series.iloc[-1]
                            if pd.notna(latest_val) and latest_val != float('inf') and latest_val != float('-inf'):
                                results[col] = float(latest_val)
                                processed_indicators += 1
                        else:
                            logger.debug(f"⚠️ Column {col} has no valid values")
                except Exception as col_error:
                    logger.debug(f"⚠️ Error processing column {col}: {str(col_error)}")

            # Categorize indicators for better organization
            categorized_results = self._categorize_indicators(results)

            logger.info(f"✅ Successfully processed {processed_indicators} indicators out of {len(indicator_columns)} generated")
            logger.info(f"📊 Indicators by category: {', '.join([f'{cat}: {len(inds)}' for cat, inds in categorized_results.items()])}")

            return {
                'method': 'strategy_all_automated',
                'indicators': results,
                'categorized_indicators': categorized_results,
                'total_indicators': len(results),
                'total_columns_generated': len(indicator_columns),
                'processed_indicators': processed_indicators,
                'strategy': 'Automated All Strategy (cores=6, timed=True)',
                'performance_settings': {
                    'cores': 6,
                    'timed': True,
                    'calculation_time_seconds': calculation_time if 'calculation_time' in locals() else None
                },
                'note': 'Automated comprehensive analysis with 280+ indicators using optimized performance settings'
            }

        except Exception as e:
            logger.error(f"❌ Error in Automated All Strategy method: {str(e)}")
            return {'error': str(e)}

    def _categorize_indicators(self, indicators: Dict) -> Dict:
        """
        Categorize indicators by their type for better organization
        """
        categories = {
            'overlap': [],
            'momentum': [],
            'volatility': [],
            'volume': [],
            'trend': [],
            'statistics': [],
            'performance': [],
            'cycles': [],
            'candles': [],
            'other': []
        }

        # Define indicator patterns for categorization
        category_patterns = {
            'overlap': ['sma', 'ema', 'wma', 'dema', 'tema', 'trima', 'kama', 'mama', 'fama', 'hma', 'linreg', 'midpoint', 'midprice', 'sar', 'sarext', 't3', 'bbands', 'dema', 'ema', 'ht_trendline', 'kama', 'ma', 'mama', 'mavp', 'midpoint', 'midprice', 'sar', 'sarext', 'sma', 't3', 'tema', 'trima', 'wma'],
            'momentum': ['rsi', 'macd', 'stoch', 'cci', 'roc', 'rocp', 'rocr', 'rocr100', 'mom', 'bop', 'cmo', 'dx', 'mfi', 'minus_di', 'minus_dm', 'plus_di', 'plus_dm', 'ppo', 'trix', 'ultosc', 'willr', 'adx', 'adxr', 'apo', 'aroon', 'aroonosc', 'bop', 'cci', 'cmo', 'dx', 'macd', 'macdext', 'macdfix', 'mfi', 'minus_di', 'minus_dm', 'mom', 'plus_di', 'plus_dm', 'ppo', 'roc', 'rocp', 'rocr', 'rocr100', 'rsi', 'stoch', 'stochf', 'stochrsi', 'trix', 'ultosc', 'willr'],
            'volatility': ['atr', 'natr', 'trange', 'bbands', 'stddev', 'var', 'atr', 'natr', 'trange'],
            'volume': ['ad', 'adosc', 'obv', 'ad', 'adosc', 'obv'],
            'trend': ['adx', 'adxr', 'aroon', 'aroonosc', 'dx', 'minus_di', 'plus_di', 'ht_dcperiod', 'ht_dcphase', 'ht_phasor', 'ht_sine', 'ht_trendmode'],
            'statistics': ['beta', 'correl', 'linearreg', 'linearreg_angle', 'linearreg_intercept', 'linearreg_slope', 'stddev', 'tsf', 'var', 'beta', 'correl', 'linearreg', 'linearreg_angle', 'linearreg_intercept', 'linearreg_slope', 'stddev', 'tsf', 'var'],
            'performance': ['pct_change', 'log_return', 'cum_return'],
            'cycles': ['ht_dcperiod', 'ht_dcphase', 'ht_phasor', 'ht_sine', 'ht_trendmode'],
            'candles': ['cdl', 'hammer', 'doji', 'engulfing', 'harami', 'piercing', 'star', 'spinning', 'hanging', 'inverted']
        }

        for indicator_name in indicators.keys():
            indicator_lower = indicator_name.lower()
            categorized = False

            for category, patterns in category_patterns.items():
                if any(pattern in indicator_lower for pattern in patterns):
                    categories[category].append(indicator_name)
                    categorized = True
                    break

            if not categorized:
                categories['other'].append(indicator_name)

        # Remove empty categories
        return {cat: inds for cat, inds in categories.items() if inds}

    def _analyze_strategy_common(self, data: pd.DataFrame) -> Dict:
        """
        Analyze using CommonStrategy (common indicators)
        """
        try:
            logger.info("🔧 Using CommonStrategy (Common Indicators)")

            # Create a copy to avoid modifying original data
            df = data.copy()

            # Check if CommonStrategy is available and has study method
            if hasattr(ta, 'CommonStrategy') and hasattr(df.ta, 'strategy'):
                # Use strategy method if available
                df.ta.strategy(ta.CommonStrategy)
            elif hasattr(ta, 'CommonStrategy'):
                # Manual implementation of common indicators
                logger.info("📝 Using manual CommonStrategy implementation")
                try:
                    # Common Price SMAs: 10, 20, 50, 200 and Volume SMA: 20
                    df.ta.sma(length=10, append=True)
                    df.ta.sma(length=20, append=True)
                    df.ta.sma(length=50, append=True)
                    df.ta.sma(length=200, append=True)
                    # Volume SMA
                    df.ta.sma(close='Volume', length=20, append=True, prefix='VOL')
                except Exception as indicator_error:
                    logger.warning(f"⚠️ Some indicators failed: {str(indicator_error)}")
            else:
                logger.error("❌ CommonStrategy not available")
                return {'error': 'CommonStrategy not available'}

            # Extract latest values
            results = {}
            indicator_columns = [col for col in df.columns if col not in ['Open', 'High', 'Low', 'Close', 'Volume']]

            for col in indicator_columns:
                latest_val = df[col].iloc[-1] if not df[col].empty else None
                if pd.notna(latest_val):
                    results[col] = float(latest_val)

            return {
                'method': 'strategy_common',
                'indicators': results,
                'total_indicators': len(results),
                'strategy': 'CommonStrategy (manual implementation)'
            }

        except Exception as e:
            logger.error(f"❌ Error in CommonStrategy method: {str(e)}")
            return {'error': str(e)}

    def _analyze_custom_strategy(self, data: pd.DataFrame) -> Dict:
        """
        Analyze using custom strategy with selected indicators
        """
        try:
            logger.info("🔧 Using Custom Strategy")

            # Create a copy to avoid modifying original data
            df = data.copy()

            # Manual implementation of custom trading strategy
            logger.info("📝 Using manual custom strategy implementation")
            try:
                # Key Moving Averages
                df.ta.sma(length=20, append=True)
                df.ta.ema(length=12, append=True)
                df.ta.ema(length=26, append=True)

                # Momentum (for signal confirmation)
                df.ta.rsi(length=14, append=True)
                df.ta.macd(append=True)
                df.ta.stoch(append=True)

                # Volatility (for breakout detection)
                df.ta.bbands(length=20, append=True)
                df.ta.atr(length=14, append=True)

                # Volume (for confirmation)
                df.ta.obv(append=True)
                try:
                    df.ta.vwap(append=True)
                except:
                    logger.warning("⚠️ VWAP not available")

                # Trend (for direction)
                df.ta.adx(length=14, append=True)
                try:
                    df.ta.supertrend(append=True)
                except:
                    logger.warning("⚠️ SuperTrend not available")

            except Exception as indicator_error:
                logger.warning(f"⚠️ Some indicators failed: {str(indicator_error)}")

            # Extract latest values
            results = {}
            indicator_columns = [col for col in df.columns if col not in ['Open', 'High', 'Low', 'Close', 'Volume']]

            for col in indicator_columns:
                latest_val = df[col].iloc[-1] if not df[col].empty else None
                if pd.notna(latest_val):
                    results[col] = float(latest_val)

            return {
                'method': 'custom_strategy',
                'indicators': results,
                'total_indicators': len(results),
                'strategy': 'Trading Signals Strategy (manual implementation)'
            }

        except Exception as e:
            logger.error(f"❌ Error in custom strategy method: {str(e)}")
            return {'error': str(e)}

    def _analyze_strategy_category(self, data: pd.DataFrame, categories: List[str] = None) -> Dict:
        """
        Analyze using category-based strategies (df.ta.strategy("category"))
        """
        try:
            logger.info("🔧 Using Strategy Category Method")

            # Create a copy to avoid modifying original data
            df = data.copy()

            # Determine which categories to process
            categories_to_process = categories if categories else ['candles', 'momentum', 'overlap', 'volatility', 'volume', 'trend']

            results_by_category = {}

            # Process each category
            for category in categories_to_process:
                try:
                    logger.info(f"📂 Processing {category} strategy...")

                    # Apply category strategy
                    if hasattr(df.ta, 'strategy'):
                        import warnings
                        with warnings.catch_warnings():
                            warnings.filterwarnings("ignore", category=FutureWarning, message=".*append.*")
                            warnings.filterwarnings("ignore", category=UserWarning, message=".*timezone.*")
                            df.ta.strategy(category)
                        results_by_category[category] = "applied"
                        logger.info(f"✅ {category} strategy applied")
                    else:
                        logger.warning(f"⚠️ Strategy method not available")
                        break

                except Exception as e:
                    logger.warning(f"⚠️ Error applying {category} strategy: {str(e)}")

            # Extract latest values from all new columns
            original_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
            indicator_columns = [col for col in df.columns if col not in original_columns]

            results = {}
            for col in indicator_columns:
                latest_val = df[col].iloc[-1] if not df[col].empty else None
                if pd.notna(latest_val):
                    results[col] = float(latest_val)

            return {
                'method': 'strategy_category',
                'indicators': results,
                'by_category': results_by_category,
                'total_indicators': len(results),
                'categories_processed': list(results_by_category.keys())
            }

        except Exception as e:
            logger.error(f"❌ Error in strategy category method: {str(e)}")
            return {'error': str(e)}

    def save_results(self, results: Dict, filename: str = None) -> str:
        """
        Save analysis results to JSON file

        Args:
            results: Analysis results dictionary
            filename: Optional filename, auto-generated if not provided

        Returns:
            Filename of saved file
        """
        try:
            if filename is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                ticker = results.get('ticker', 'unknown')
                date = results.get('date', 'unknown')
                filename = f"technical_analysis_{ticker}_{date}_{timestamp}.json"

            with open(filename, 'w') as f:
                json.dump(results, f, indent=2, default=str)

            logger.info(f"✅ Results saved to {filename}")
            return filename

        except Exception as e:
            logger.error(f"❌ Error saving results: {str(e)}")
            return ""

    def print_summary(self, results: Dict):
        """
        Print a summary of analysis results
        """
        try:
            print(f"\n{'='*60}")
            print(f"📊 TECHNICAL INDICATORS ANALYSIS SUMMARY")
            print(f"{'='*60}")

            print(f"🎯 Ticker: {results.get('ticker', 'N/A')}")
            print(f"📅 Date: {results.get('date', 'N/A')}")
            print(f"🔧 Method: {results.get('method', 'N/A')}")

            if 'signals' in results:
                print(f"📈 Total Signals: {results.get('total_signals', 0)}")

            if 'analysis' in results:
                analysis = results['analysis']
                if isinstance(analysis, list):
                    print(f"📊 Analyzed Candles: {len(analysis)}")
                    for i, candle_analysis in enumerate(analysis):
                        if 'indicators' in candle_analysis:
                            print(f"   Candle {i+1}: {len(candle_analysis['indicators'])} indicators")
                elif isinstance(analysis, dict) and 'indicators' in analysis:
                    print(f"📊 Total Indicators: {len(analysis['indicators'])}")

            print(f"{'='*60}\n")

        except Exception as e:
            logger.error(f"❌ Error printing summary: {str(e)}")


def create_cli_parser():
    """Create command line interface parser"""
    parser = argparse.ArgumentParser(
        description="Technical Indicators Analyzer using pandas-ta",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze signals from backtester
  python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025

  # Analyze specific candles
  python technical_indicators_analyzer.py --mode candles --ticker BATAINDIA --exchange BSE --date 24-06-2025 --times "12:23,15:42"

  # Analyze time period
  python technical_indicators_analyzer.py --mode period --ticker BATAINDIA --exchange BSE --date 24-06-2025 --start-time "10:15" --end-time "15:00"

  # Analyze full market session
  python technical_indicators_analyzer.py --mode full --ticker BATAINDIA --exchange BSE --date 24-06-2025

  # Use different analysis methods
  python technical_indicators_analyzer.py --mode signals --ticker BATAINDIA --exchange BSE --date 24-06-2025 --method study
        """
    )

    parser.add_argument('--mode', choices=['signals', 'candles', 'period', 'full'],
                       help='Analysis mode')
    parser.add_argument('--ticker', help='Stock ticker symbol')
    parser.add_argument('--exchange', help='Exchange name (BSE, NSE, etc.)')
    parser.add_argument('--date', help='Date in DD-MM-YYYY format')

    parser.add_argument('--method', choices=['direct_call', 'extension', 'extension_kind', 'strategy_all', 'strategy_all_automated', 'strategy_common', 'strategy_category', 'custom_strategy'],
                       default='extension', help='Analysis method (default: extension)')

    # Category selection
    parser.add_argument('--categories', help='Comma-separated list of categories to include (overlap,momentum,volatility,volume,trend,statistics,performance,cycles,candles)')
    parser.add_argument('--exclude-categories', help='Comma-separated list of categories to exclude')
    parser.add_argument('--list-categories', action='store_true', help='List all available categories and exit')

    # Mode-specific arguments
    parser.add_argument('--times', help='Comma-separated candle times in HH:MM format (for candles mode)')
    parser.add_argument('--start-time', help='Start time in HH:MM format (for period mode)')
    parser.add_argument('--end-time', help='End time in HH:MM format (for period mode)')

    # Options
    parser.add_argument('--no-history', action='store_true',
                       help='Disable 2-minute historical data inclusion')
    parser.add_argument('--save', help='Save results to specified filename')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')

    return parser


def main():
    """Main function for CLI execution"""
    parser = create_cli_parser()
    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize analyzer
    analyzer = TechnicalIndicatorsAnalyzer()

    # Handle list categories option
    if args.list_categories:
        print("📂 Available Categories:")
        for i, category in enumerate(analyzer.categories, 1):
            indicators_count = len(analyzer.category_indicators.get(category, []))
            print(f"  {i}. {category} ({indicators_count} indicators)")
        print(f"\n💡 Usage: --categories overlap,momentum,volatility")
        print(f"💡 Usage: --exclude-categories candles,cycles")
        return

    # Validate required arguments for analysis modes
    if not all([args.mode, args.ticker, args.exchange, args.date]):
        print("❌ Error: --mode, --ticker, --exchange, and --date are required for analysis")
        print("💡 Use --list-categories to see available categories")
        return

    # Process category selection
    categories = None
    if args.categories:
        categories = [cat.strip() for cat in args.categories.split(',')]
        # Validate categories
        invalid_cats = [cat for cat in categories if cat not in analyzer.categories]
        if invalid_cats:
            print(f"❌ Invalid categories: {', '.join(invalid_cats)}")
            print(f"📂 Available categories: {', '.join(analyzer.categories)}")
            return
    elif args.exclude_categories:
        exclude_cats = [cat.strip() for cat in args.exclude_categories.split(',')]
        categories = [cat for cat in analyzer.categories if cat not in exclude_cats]

    # Execute based on mode
    results = None
    include_history = not args.no_history

    try:
        if args.mode == 'signals':
            print(f"🔍 Analyzing signals from backtester for {args.ticker} on {args.date}")
            results = analyzer.analyze_signals_from_backtester(
                ticker=args.ticker,
                exchange=args.exchange,
                date=args.date,
                method=args.method,
                include_history=include_history,
                categories=categories
            )

        elif args.mode == 'candles':
            if not args.times:
                print("❌ Error: --times argument required for candles mode")
                return

            candle_times = [time.strip() for time in args.times.split(',')]
            print(f"🔍 Analyzing specific candles for {args.ticker} on {args.date}")
            print(f"🕐 Times: {', '.join(candle_times)}")

            results = analyzer.analyze_specific_candles(
                ticker=args.ticker,
                exchange=args.exchange,
                date=args.date,
                candle_times=candle_times,
                method=args.method,
                include_history=include_history,
                categories=categories
            )

        elif args.mode == 'period':
            if not args.start_time or not args.end_time:
                print("❌ Error: --start-time and --end-time arguments required for period mode")
                return

            print(f"🔍 Analyzing time period {args.start_time}-{args.end_time} for {args.ticker} on {args.date}")
            results = analyzer.analyze_time_period(
                ticker=args.ticker,
                exchange=args.exchange,
                date=args.date,
                start_time=args.start_time,
                end_time=args.end_time,
                method=args.method,
                categories=categories
            )

        elif args.mode == 'full':
            print(f"🔍 Analyzing full market session for {args.ticker} on {args.date}")
            results = analyzer.analyze_full_market_session(
                ticker=args.ticker,
                exchange=args.exchange,
                date=args.date,
                method=args.method,
                categories=categories
            )

        # Display results
        if results:
            analyzer.print_summary(results)

            # Save results if requested
            if args.save:
                saved_file = analyzer.save_results(results, args.save)
                if saved_file:
                    print(f"💾 Results saved to: {saved_file}")
            else:
                # Auto-save with timestamp
                saved_file = analyzer.save_results(results)
                if saved_file:
                    print(f"💾 Results auto-saved to: {saved_file}")
        else:
            print("❌ No results generated")

    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
    except Exception as e:
        logger.error(f"❌ Error in main execution: {str(e)}")
        print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    main()
